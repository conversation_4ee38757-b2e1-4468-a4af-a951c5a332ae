try:
    import bpy
except ImportError:
    bpy = None
import sys
import os
import time

# Add project root to sys.path for imports
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.main import ProShooto

def run_proshooto_pipeline(script_path: str, output_dir: str):
    pipeline = ProShooto()
    pipeline.process_script(script_path, output_dir)
    print(f"ProShooto pipeline completed for script: {script_path}")

if __name__ == "__main__":
    import logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)s %(message)s')
    logger = logging.getLogger(__name__)

    # Example usage: pass script path and output directory as arguments
    argv = sys.argv
    # Blender passes all args after '--' to the script
    if "--" in argv:
        argv = argv[argv.index("--") + 1:]
    else:
        argv = []

    if len(argv) < 2:
        logger.error("Usage: blender --background --python blender_runner.py -- <script_path> <output_dir>")
    else:
        script_path = argv[0]
        output_dir = argv[1]
        start_time = time.time()
        logger.info(f"Starting ProShooto pipeline for script: {script_path}")
        run_proshooto_pipeline(script_path, output_dir)
        logger.info(f"Total time: {time.time() - start_time:.2f} seconds")
