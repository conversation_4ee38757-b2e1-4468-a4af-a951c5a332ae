import os
import tempfile
import pytest
from src.core.shooting_script_converter import ShootingScriptGenerator
from src.main import ProShooto

sample_script = """
INT. COFFEE SHOP - DAY

The bell on the door JANGLES as <PERSON><PERSON><PERSON>, mid-30s, disheveled, enters. He scans the room nervously.

[PROPS: Coffee cup, newspaper]
[WARDROBE: Wrinkled shirt, jeans]

SARAH, early 30s, stylish, sits at a corner table. She sips her latte, pretending not to notice him.

<PERSON><PERSON><PERSON>
(angry)
You said you'd be here at nine.

The other PATRONS glance up from their drinks.
"""

def test_shooting_script_generator_basic():
    generator = ShootingScriptGenerator(sample_script)
    generator.parse_original_script()
    generator.add_shooting_details()
    docs = generator.generate_production_docs()

    assert 'shooting_script' in docs
    assert 'shot_list' in docs
    assert 'scene_breakdown' in docs
    assert 'props_list' in docs
    assert 'schedule' in docs
    assert 'COFFEE SHOP' in docs['shooting_script']

def test_proshooto_convert_to_shooting_script(tmp_path):
    script_file = tmp_path / "sample.fountain"
    script_file.write_text(sample_script, encoding='utf-8')

    output_dir = tmp_path / "output"
    output_dir.mkdir()

    pipeline = ProShooto()
    pipeline.convert_to_shooting_script(str(script_file), str(output_dir))

    shooting_script_path = output_dir / "shooting_script.txt"
    shot_list_path = output_dir / "shot_list.txt"
    production_docs_path = output_dir / "production_docs.txt"

    assert shooting_script_path.exists()
    assert shot_list_path.exists()
    assert production_docs_path.exists()

    shooting_script_content = shooting_script_path.read_text(encoding='utf-8')
    assert "COFFEE SHOP" in shooting_script_content
