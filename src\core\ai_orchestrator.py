import anthropic
import openai
import json
import re
from typing import Dict, List, Optional
from tenacity import retry, stop_after_attempt, wait_exponential
from src.utils.logging import get_logger

logger = get_logger(__name__)

class AIOrchestrator:
    def __init__(self, config: Dict):
        self.claude = anthropic.Client(api_key=config['claude_api_key'])
        self.openai = openai.Client(api_key=config['openai_api_key'])
        self.config = config
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def analyze_script(self, script_data: Dict) -> Dict:
        """Generate asset manifest from script analysis"""
        prompt = self._build_analysis_prompt(script_data)
        response = self.claude.completions.create(
            prompt=prompt,
            model=self.config.get('claude_model', 'claude-2'),
            max_tokens_to_sample=4000
        )
        return self._parse_manifest(response.completion)
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def generate_concept_prompt(self, asset: Dict) -> str:
        """Create prompt for concept art generation"""
        response = self.claude.completions.create(
            prompt=self._build_concept_prompt(asset),
            model=self.config.get('claude_model', 'claude-2'),
            max_tokens_to_sample=2000
        )
        return self._extract_prompt(response.completion)
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def generate_blender_code(self, asset: Dict, concept_desc: str) -> str:
        """Generate Blender Python API code"""
        response = self.openai.chat.completions.create(
            model="gpt-4-1106-preview",
            messages=[{
                "role": "system",
                "content": self._build_blender_system_prompt()
            }, {
                "role": "user",
                "content": self._build_blender_user_prompt(asset, concept_desc)
            }],
            temperature=0.3,
            max_tokens=3000
        )
        return self._extract_code(response.choices[0].message.content)
    
    def _build_analysis_prompt(self, script_data: Dict) -> str:
        """Construct prompt for script analysis"""
        return f"""
        Analyze this screenplay and generate a detailed 3D asset manifest.
        
        Script Metadata:
        Title: {script_data.get('metadata', {}).get('title', 'Untitled')}
        Scenes: {len(script_data.get('scenes', []))}
        
        Key Asset References:
        {json.dumps(script_data.get('assets', {}), indent=2)}
        
        Output JSON format:
        {{
            "assets": [
                {{
                    "name": "Asset Name",
                    "type": "character|prop|environment",
                    "description": "Detailed description",
                    "importance": 1-5,
                    "reusable": bool,
                    "variations": [
                        {{"material": "wood", "color": "dark"}}
                    ],
                    "reference_images": []
                }}
            ]
        }}
        """
    
    def _parse_manifest(self, text: str) -> Dict:
        """Extract JSON from AI response"""
        try:
            json_str = re.search(r'\{.*\}', text, re.DOTALL).group()
            return json.loads(json_str)
        except Exception as e:
            logger.error(f"Failed to parse manifest: {str(e)}")
            raise ValueError("Invalid manifest format")
    
    def _extract_code(self, text: str) -> str:
        """Extract Python code block from response"""
        code_block = re.search(r'```python(.*?)```', text, re.DOTALL)
        if not code_block:
            raise ValueError("No valid code block found")
        return code_block.group(1).strip()