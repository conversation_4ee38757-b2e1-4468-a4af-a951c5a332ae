import pytest
from unittest.mock import patch, MagicMock
from src.core.ai_orchestrator import AIOrchestrator
from src.interfaces.comfyui_api import ComfyUIAPI
from src.core.assets_builder import AssetBuilder

@pytest.fixture
def config():
    return {
        'claude_api_key': 'fake_key',
        'openai_api_key': 'fake_key',
        'comfyui_url': 'http://localhost:8000',
        'blender_exec': '/path/to/blender'
    }

def test_ai_orchestrator_init(config):
    ai = AIOrchestrator(config)
    assert ai is not None

@patch('src.core.ai_orchestrator.requests.post')
def test_ai_orchestrator_api_failure(mock_post, config):
    mock_post.side_effect = Exception("API failure")
    ai = AIOrchestrator(config)
    with pytest.raises(Exception):
        ai.analyze_script({'dummy': 'data'})

@patch.object(ComfyUIAPI, 'generate_concept', side_effect=TimeoutError("Timeout"))
def test_comfyui_api_timeout(mock_generate_concept):
    api = ComfyUIAPI('http://localhost:8000', '/fake/workflow/dir')
    with pytest.raises(TimeoutError):
        api.generate_concept('prompt', 'type')

def test_comfyui_api_init():
    api = ComfyUIAPI('http://localhost:8000', '/fake/workflow/dir')
    # Pylance may not recognize 'url' attribute, but it exists at runtime
    assert hasattr(api, 'url')
    assert getattr(api, 'url', None) == 'http://localhost:8000'
    assert api.workflow_dir == '/fake/workflow/dir'

def test_asset_builder_init():
    builder = AssetBuilder('/fake/path/parametric_assets.blend')
    # Pylance may not recognize 'template_path' attribute, but it exists at runtime
    assert hasattr(builder, 'template_path')
    assert getattr(builder, 'template_path', None) == '/fake/path/parametric_assets.blend'
