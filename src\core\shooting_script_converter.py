import re
from datetime import datetime
from enum import Enum

class SceneType(Enum):
    INT = "INTERIOR"
    EXT = "EXTERIOR"
    INT_EXT = "INTERIOR/EXTERIOR"

class ShotType(Enum):
    WIDE = "WIDE SHOT"
    MEDIUM = "MEDIUM SHOT"
    CLOSEUP = "CLOSE UP"
    ECU = "EXTREME CLOSE UP"
    POV = "POV SHOT"
    DOLLY = "DOLLY SHOT"
    CRANE = "CRANE SHOT"
    STEADICAM = "STEADICAM SHOT"

class ShootingScriptGenerator:
    def __init__(self, original_script):
        self.original_script = original_script
        self.shooting_script = []
        self.scene_breakdown = {}
        self.shot_list = []
        self.current_scene_number = 1
        
    def parse_original_script(self):
        """Parse the original screenplay into components"""
        # This would use regex to identify scene headings, action, dialogue, etc.
        scenes = re.split(r'\\n\\n\\n+', self.original_script)
        for scene in scenes:
            self._process_scene(scene)
    
    def _process_scene(self, scene_text):
        """Process an individual scene"""
        import logging
        logger = logging.getLogger(__name__)
        lines = scene_text.split('\\n')
        scene_info = {
            'number': self.current_scene_number,
            'location': '',
            'day_night': 'DAY',
            'type': SceneType.INT,
            'summary': '',
            'cast': set(),
            'props': set(),
            'wardrobe': set(),
            'shots': []
        }
        
        # Extract scene heading
        line0 = lines[0].strip()
        logger.debug(f"Processing scene heading line: {line0}")
        heading_match = re.match(r'^(INT\.|EXT\.|INT/EXT\.)\s+(.+?)\s*-\s*(DAY|NIGHT|DUSK|DAWN)', line0)
        if heading_match:
            logger.debug(f"Heading match groups: {heading_match.groups()}")
            scene_info['type'] = SceneType[heading_match.group(1).replace('/', '_').replace('.', '')]
            scene_info['location'] = heading_match.group(2)
            scene_info['day_night'] = heading_match.group(3)
        
        # Process remaining lines
        for line in lines[1:]:
            if line.strip().startswith('[') and line.strip().endswith(']'):
                # Production note
                note = line.strip()[1:-1]
                if 'PROPS:' in note:
                    scene_info['props'].update(p.strip() for p in note.split('PROPS:')[1].split(','))
                elif 'WARDROBE:' in note:
                    scene_info['wardrobe'].update(w.strip() for w in note.split('WARDROBE:')[1].split(','))
            elif line.strip().isupper() and not line.strip().endswith('.'):
                # Character name
                scene_info['cast'].add(line.strip())
            elif line.strip():
                # Action or dialogue
                scene_info['summary'] += line.strip() + ' '
        
        self.scene_breakdown[self.current_scene_number] = scene_info
        self.current_scene_number += 1
    
    def add_shooting_details(self):
        """Add technical shooting details to each scene"""
        for scene_num, scene in self.scene_breakdown.items():
            # Determine shot types based on scene content
            shots = self._determine_shots(scene)
            scene['shots'] = shots
            self.shot_list.extend([(scene_num, shot) for shot in shots])
            
            # Build shooting script entry
            shooting_scene = self._format_shooting_scene(scene)
            self.shooting_script.append(shooting_scene)
    
    def _determine_shots(self, scene):
        """Determine appropriate shots for a scene based on its content"""
        shots = []
        
        # Start with establishing shot
        shots.append({
            'number': 1,
            'type': ShotType.WIDE,
            'description': f"Establish {scene['location']}",
            'camera_move': '',
            'lens': '24mm',
            'duration': '5-8s'
        })
        
        # Add coverage based on scene content
        if len(scene['cast']) > 2:
            shots.append({
                'number': 2,
                'type': ShotType.MEDIUM,
                'description': "Group shot",
                'camera_move': 'Pan',
                'lens': '35mm',
                'duration': ''
            })
        
        # Add closeups for important dialogue
        if "angry" in scene['summary'].lower():
            shots.append({
                'number': len(shots) + 1,
                'type': ShotType.CLOSEUP,
                'description': "Angry reaction",
                'camera_move': '',
                'lens': '50mm',
                'duration': ''
            })
        
        return shots
    
    def _format_shooting_scene(self, scene):
        """Format a scene with shooting details"""
        formatted = []
        
        # Scene heading with number
        formatted.append(f"{scene['number']}. {scene['type'].value}. {scene['location']} - {scene['day_night']}")
        
        # Scene summary
        formatted.append(f"[SUMMARY: {scene['summary'][:100]}...]")
        
        # Cast list
        formatted.append(f"[CAST: {', '.join(scene['cast'])}]")
        
        # Shots
        for shot in scene['shots']:
            formatted.append(f"SHOT {shot['number']}: {shot['type'].value} - {shot['description']}")
            formatted.append(f"  Camera: {shot['lens']} {'(' + shot['camera_move'] + ')' if shot['camera_move'] else ''}")
            if shot['duration']:
                formatted.append(f"  Duration: {shot['duration']}")
        
        # Props and wardrobe
        if scene['props']:
            formatted.append(f"[PROPS: {', '.join(scene['props'])}]")
        if scene['wardrobe']:
            formatted.append(f"[WARDROBE: {', '.join(scene['wardrobe'])}]")
        
        return '\\n'.join(formatted)
    
    def generate_production_docs(self):
        """Generate production documents from the breakdown"""
        # Shot list
        shot_list = "\\n".join(
            f"Scene {s[0]}, Shot {i+1}: {s[1]['type'].value} - {s[1]['description']}"
            for i, s in enumerate(self.shot_list)
        )
        
        # Scene breakdown
        breakdown = "\\n\\n".join(
            f"Scene {num}:\\n"
            f"Location: {scene['location']}\\n"
            f"Time: {scene['day_night']}\\n"
            f"Cast: {', '.join(scene['cast'])}\\n"
            f"Shots: {len(scene['shots'])}\\n"
            f"Summary: {scene['summary'][:150]}..."
            for num, scene in self.scene_breakdown.items()
        )
        
        return {
            'shooting_script': '\\n\\n'.join(self.shooting_script),
            'shot_list': shot_list,
            'scene_breakdown': breakdown,
            'props_list': self._generate_props_list(),
            'schedule': self._generate_schedule()
        }
    
    def _generate_props_list(self):
        """Generate consolidated props list"""
        props = set()
        for scene in self.scene_breakdown.values():
            props.update(scene['props'])
        return "\\n".join(sorted(props))
    
    def _generate_schedule(self):
        """Generate a simple shooting schedule"""
        # This would be more sophisticated in a real implementation
        days = []
        scenes_per_day = 5  # Average
        scene_numbers = sorted(self.scene_breakdown.keys())
        
        for i in range(0, len(scene_numbers), scenes_per_day):
            day_scenes = scene_numbers[i:i+scenes_per_day]
            days.append(f"Day {len(days)+1}: Scenes {', '.join(str(s) for s in day_scenes)}")
        
        return "\\n".join(days)
