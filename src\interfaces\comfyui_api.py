import requests
import json
from pathlib import Path
from typing import Dict, Optional
from src.utils.logging import get_logger

logger = get_logger(__name__)

class ComfyUIAPI:
    def __init__(self, base_url: str, workflow_dir: str):
        self.base_url = base_url.rstrip('/')
        self.workflow_dir = workflow_dir
    
    def generate_concept(self, prompt: str, asset_type: str) -> Optional[str]:
        """Generate concept art using ComfyUI"""
        workflow = self._load_workflow_template(asset_type)
        if not workflow:
            return None
            
        payload = self._build_payload(workflow, prompt)
        try:
            response = requests.post(f"{self.base_url}/prompt", json=payload)
            response.raise_for_status()
            return self._get_result_image(response.json())
        except Exception as e:
            logger.error(f"ComfyUI generation failed: {str(e)}")
            return None
    
    def _load_workflow_template(self, asset_type: str) -> Optional[Dict]:
        """Load workflow JSON for asset type"""
        template_path = Path(self.workflow_dir) / f"{asset_type}.json"
        try:
            with open(template_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Failed to load workflow template: {str(e)}")
            return None
    
    def _build_payload(self, workflow: Dict, prompt: str) -> Dict:
        """Inject prompt into workflow"""
        # Find text input node (varies by workflow)
        for node_id, node in workflow.items():
            if node.get('class_type') == 'CLIPTextEncode':
                node['inputs']['text'] = prompt
        return {
            "prompt": workflow,
            "client_id": "proshooto"
        }
    
    def _get_result_image(self, response: Dict) -> str:
        """Extract image filename from response"""
        # This requires ComfyUI to be configured with proper output handling
        return response.get('output', {}).get('images', [{}])[0].get('filename')