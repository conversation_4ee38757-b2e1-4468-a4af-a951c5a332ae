import pytest
from unittest.mock import patch

@pytest.fixture
def mock_subprocess_run():
    with patch('subprocess.run') as mock_run:
        yield mock_run

def test_subprocess_called(mock_subprocess_run):
    import src.main as main_module
    # Example test to check subprocess.run is called in _process_asset
    pipeline = main_module.ProShooto()
    asset = {'name': 'test_asset', 'type': 'test_type'}
    output_dir = 'test_output'
    pipeline._process_asset(asset, output_dir)
    assert mock_subprocess_run.called
