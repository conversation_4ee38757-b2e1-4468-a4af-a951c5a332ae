import pytest
import os
from src.main import ProShooto

@pytest.mark.skipif(
    not os.getenv('BLENDER_EXECUTABLE') or not os.getenv('COMFYUI_URL'),
    reason="BLENDER_EXECUTABLE or COMFYUI_URL environment variables not set"
)
def test_full_pipeline_integration():
    pipeline = ProShooto()
    script_path = "examples/scripts/sample.fountain"
    output_dir = "output_assets"
    
    # Run the full pipeline; this requires Blender and ComfyUI to be properly configured
    pipeline.process_script(script_path, output_dir)
    
    # Check that output directory exists and contains .blend files
    assert os.path.exists(output_dir)
    blend_files = [f for f in os.listdir(output_dir) if f.endswith('.blend')]
    assert len(blend_files) > 0
