import os
import time
import subprocess
import tempfile
from dotenv import load_dotenv
from concurrent.futures import ThreadPoolExecutor, wait
from typing import Dict
import logging
from src.core.script_parser import ScriptParser
from src.core.ai_orchestrator import AIOrchestrator
from src.interfaces.comfyui_api import ComfyUIAPI
from src.core.assets_builder import AssetBuilder
from src.utils.logging import setup_logging

logger = logging.getLogger(__name__)

class ProShooto:
    def __init__(self):
        load_dotenv()
        setup_logging()
        
        self.config = {
            'claude_api_key': os.getenv('CLAUDE_API_KEY'),
            'openai_api_key': os.getenv('OPENAI_API_KEY'),
            'comfyui_url': os.getenv('COMFYUI_URL'),
            'blender_exec': os.getenv('BLENDER_EXECUTABLE')
        }
        
        self.parser = ScriptParser()
        self.ai = AIOrchestrator(self.config)
        if not self.config['comfyui_url']:
            raise ValueError("COMFYUI_URL environment variable is not set")
        workflow_dir = os.getenv('COMFYUI_WORKFLOW_DIR')
        if not workflow_dir:
            raise ValueError("COMFYUI_WORKFLOW_DIR environment variable is not set")
        self.comfyui = ComfyUIAPI(
            self.config['comfyui_url'],
            workflow_dir
        )
        blender_templates = os.getenv('BLENDER_TEMPLATES')
        if not blender_templates:
            raise ValueError("BLENDER_TEMPLATES environment variable is not set")
        self.builder = AssetBuilder(
            os.path.join(blender_templates, 'parametric_assets.blend')
        )
    
    def process_script(self, script_path: str, output_dir: str):
        """Main processing pipeline"""
        start_time = time.time()
        try:
            # Step 1: Parse script
            script_data = self.parser.parse(script_path)
            
            # Step 2: Generate asset manifest
            manifest = self.ai.analyze_script(script_data)
            
            # Step 3: Process assets in parallel
            with ThreadPoolExecutor(max_workers=3) as executor:
                futures = []
                for asset in manifest.get('assets', [])[:5]:  # Limit for demo
                    futures.append(executor.submit(self._process_asset, asset, output_dir))
                wait(futures)
            
            logger.info(f"Pipeline completed in {time.time() - start_time:.2f}s")
        except Exception as e:
            logger.error(f"Error in processing script pipeline: {e}")

    def convert_to_shooting_script(self, script_path: str, output_dir: str):
        """Convert narrative screenplay to shooting script and generate production documents"""
        import os
        from src.core.shooting_script_converter import ShootingScriptGenerator

        try:
            with open(script_path, 'r', encoding='utf-8') as f:
                original_script = f.read()
            
            generator = ShootingScriptGenerator(original_script)
            generator.parse_original_script()
            generator.add_shooting_details()
            production_docs = generator.generate_production_docs()

            shooting_script_path = os.path.join(output_dir, 'shooting_script.txt')
            shot_list_path = os.path.join(output_dir, 'shot_list.txt')
            production_docs_path = os.path.join(output_dir, 'production_docs.txt')

            os.makedirs(output_dir, exist_ok=True)

            with open(shooting_script_path, 'w', encoding='utf-8') as f:
                f.write(production_docs['shooting_script'])
            
            with open(shot_list_path, 'w', encoding='utf-8') as f:
                f.write(production_docs['shot_list'])
            
            with open(production_docs_path, 'w', encoding='utf-8') as f:
                f.write("=== SCENE BREAKDOWN ===\n")
                f.write(production_docs['scene_breakdown'])
                f.write("\n\n=== PROPS LIST ===\n")
                f.write(production_docs['props_list'])
                f.write("\n\n=== SCHEDULE ===\n")
                f.write(production_docs['schedule'])
            
            logger.info("Shooting script and production documents generated successfully!")
        except Exception as e:
            logger.error(f"Failed to convert to shooting script: {e}")

    def _process_asset(self, asset: Dict, output_dir: str):
        """Process individual asset"""
        try:
            # Generate concept art
            concept_prompt = self.ai.generate_concept_prompt(asset)
            concept_image = self.comfyui.generate_concept(concept_prompt, asset['type'])
            
            # Generate Blender code
            blender_code = self.ai.generate_blender_code(asset, concept_prompt)
            
            # Save blender code to temp script
            with tempfile.NamedTemporaryFile('w', suffix='.py', delete=False) as script_file:
                script_file.write(blender_code)
                script_path = script_file.name
            
            # Prepare output path
            asset_path = os.path.join(output_dir, f"{asset['name']}.blend")
            
            # Run blender headlessly to build asset
            blender_exec = self.config.get('blender_exec')
            if not blender_exec:
                raise ValueError("Blender executable path not set in config")
            
            subprocess.run([
                blender_exec,
                '--background',
                '--python', script_path,
                '--',  # args after this are passed to the script
                asset_path
            ], check=True)
        except Exception as e:
            logger.error(f"Failed to process {asset['name']}: {str(e)}")

if __name__ == "__main__":
    pipeline = ProShooto()
    pipeline.process_script(
        script_path="examples/scripts/sample.fountain",
        output_dir="output_assets"
    )
