import sys
import types
import json
from typing import Dict, List
from src.utils.logging import get_logger

try:
    import bpy
    from mathutils import Vector
except ImportError:
    # Mock bpy and Vector for IDE and test environment to suppress Pylance errors
    bpy = types.SimpleNamespace()
    bpy.data = types.SimpleNamespace()
    bpy.data.node_groups = {}
    bpy.data.objects = {}
    bpy.data.materials = {}
    bpy.ops = types.SimpleNamespace()
    Vector = object

logger = get_logger(__name__)

class AssetBuilder:
    def __init__(self, template_path: str):
        self._load_template(template_path)
        self.material_library = {}
    
    def build_from_code(self, code: str) -> bool:
        """Execute generated Blender code with safety checks"""
        try:
            safe_locals = {'bpy': bpy, 'Vector': Vector}
            exec(code, {'__builtins__': None}, safe_locals)
            return True
        except Exception as e:
            logger.error(f"Blender code execution failed: {str(e)}")
            return False
    
    def apply_geometry_nodes(self, asset_name: str, parameters: Dict):
        """Add parametric controls to asset"""
        obj = bpy.data.objects.get(asset_name)
        if not obj:
            raise ValueError(f"Object {asset_name} not found")
        
        modifier = obj.modifiers.new("ProShooto_GN", 'NODES')
        modifier.node_group = self._get_geometry_node_group(parameters)
        self._set_gn_parameters(modifier, parameters)
    
    def create_material_variants(self, base_material: str, variants: List[Dict]):
        """Generate material variations"""
        if base_material not in bpy.data.materials:
            raise ValueError(f"Material {base_material} not found")
        
        for variant in variants:
            mat = bpy.data.materials[base_material].copy()
            mat.name = f"{base_material}_{variant.get('name', 'variant')}"
            
            # Configure material nodes
            self._configure_material(mat, variant)
            self.material_library[mat.name] = mat
    
    def _load_template(self, path: str):
        """Load Blender template file"""
        try:
            bpy.ops.wm.open_mainfile(filepath=path)
        except Exception as e:
            logger.warning(f"Could not load template: {str(e)}")
    
    def _get_geometry_node_group(self, params: Dict) -> 'bpy.types.NodeGroup':
        """Get or create geometry node group"""
        group_name = "ProShooto_Params"
        if group_name in bpy.data.node_groups:
            return bpy.data.node_groups[group_name]
        
        group = bpy.data.node_groups.new(group_name, 'GeometryNodeTree')
        
        # Create group inputs/outputs
        group.inputs.new('NodeSocketFloat', 'Scale')
        group.inputs.new('NodeSocketColor', 'BaseColor')
        
        # Add sample geometry nodes
        # (In practice this would be more complex)
        return group

# Fix for AttributeError: module 'types' has no attribute 'NodeGroup' during Blender test runs
import types
if not hasattr(types, 'NodeGroup'):
    types.NodeGroup = type('NodeGroup', (), {})

class AssetBuilder:
    def __init__(self, template_path: str):
        self._load_template(template_path)
        self.material_library = {}

    def build_from_code(self, code: str) -> bool:
        """Execute generated Blender code with safety checks"""
        try:
            safe_locals = {'bpy': bpy, 'Vector': Vector}
            exec(code, {'__builtins__': None}, safe_locals)
            return True
        except Exception as e:
            logger.error(f"Blender code execution failed: {str(e)}")
            return False

    def apply_geometry_nodes(self, asset_name: str, parameters: Dict):
        """Add parametric controls to asset"""
        obj = bpy.data.objects.get(asset_name)
        if not obj:
            raise ValueError(f"Object {asset_name} not found")

        modifier = obj.modifiers.new("ProShooto_GN", 'NODES')
        modifier.node_group = self._get_geometry_node_group(parameters)
        self._set_gn_parameters(modifier, parameters)

    def create_material_variants(self, base_material: str, variants: List[Dict]):
        """Generate material variations"""
        if base_material not in bpy.data.materials:
            raise ValueError(f"Material {base_material} not found")

        for variant in variants:
            mat = bpy.data.materials[base_material].copy()
            mat.name = f"{base_material}_{variant.get('name', 'variant')}"

            # Configure material nodes
            self._configure_material(mat, variant)
            self.material_library[mat.name] = mat

    def _load_template(self, path: str):
        """Load Blender template file"""
        try:
            bpy.ops.wm.open_mainfile(filepath=path)
        except Exception as e:
            logger.warning(f"Could not load template: {str(e)}")

    def _get_geometry_node_group(self, params: Dict) -> 'bpy.types.NodeGroup':
        """Get or create geometry node group"""
        group_name = "ProShooto_Params"
        if group_name in bpy.data.node_groups:
            return bpy.data.node_groups[group_name]

        group = bpy.data.node_groups.new(group_name, 'GeometryNodeTree')

        # Create group inputs/outputs
        group.inputs.new('NodeSocketFloat', 'Scale')
        group.inputs.new('NodeSocketColor', 'BaseColor')

        # Add sample geometry nodes
        # (In practice this would be more complex)
        return group

    def _set_gn_parameters(self, modifier, params: Dict):
        """Set geometry node values"""
        for key, value in params.items():
            if key in modifier:
                modifier[key] = value

    def _configure_material(self, material, variant: Dict):
        """Configure material nodes based on variant"""
        nodes = material.node_tree.nodes
        principled = next((n for n in nodes if n.type == 'BSDF_PRINCIPLED'), None)

        if principled and 'color' in variant:
            principled.inputs['Base Color'].default_value = self._parse_color(variant['color'])

        if principled and 'roughness' in variant:
            principled.inputs['Roughness'].default_value = variant['roughness']

    def _parse_color(self, color_value):
        """Parse color input to Blender color format"""
        # Assuming color_value is a hex string like '#RRGGBB'
        if isinstance(color_value, str) and color_value.startswith('#') and len(color_value) == 7:
            r = int(color_value[1:3], 16) / 255.0
            g = int(color_value[3:5], 16) / 255.0
            b = int(color_value[5:7], 16) / 255.0
            return (r, g, b, 1.0)  # RGBA with alpha=1
        # Default to white if parsing fails
        return (1.0, 1.0, 1.0, 1.0)
