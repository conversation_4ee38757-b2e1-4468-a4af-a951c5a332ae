# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from typing import Union
from typing_extensions import Annotated, TypeAlias

from .._utils import PropertyInfo
from .text_block import TextBlock
from .thinking_block import Thinking<PERSON><PERSON>
from .tool_use_block import Too<PERSON><PERSON><PERSON><PERSON><PERSON>
from .server_tool_use_block import ServerTool<PERSON><PERSON><PERSON><PERSON>
from .redacted_thinking_block import RedactedThinking<PERSON>lock
from .web_search_tool_result_block import WebSearchToolResultBlock

__all__ = ["ContentBlock"]

ContentBlock: TypeAlias = Annotated[
    Union[TextBlock, ToolUseBlock, ServerToolUseBlock, WebSearchToolResultBlock, Thinking<PERSON>lock, RedactedThinking<PERSON>lock],
    PropertyInfo(discriminator="type"),
]
