import re
import json
from pathlib import Path
from enum import Enum
import pypdf
import logging
import fountain
import xml.etree.ElementTree as ET
from typing import Dict, List, Optional
import importlib

# Fix for fountain package LOCALE flag issue in Blender environment
# Patch the re.compile call in fountain.main to remove re.L flag

import re

# Patch re.compile to remove re.L flag when pattern is str to avoid Blender error
original_compile = re.compile

def patched_compile(pattern, flags=0):
    if isinstance(pattern, str) and (flags & re.L):
        flags = flags & ~re.L
    return original_compile(pattern, flags)

re.compile = patched_compile

# More robust patch for fountain.main to avoid LOCALE flag issue in Blender environment
import fountain.main

# Patch re.compile to remove re.L flag when pattern is str to avoid Blender error
original_re_compile = re.compile

def patched_re_compile(pattern, flags=0):
    if isinstance(pattern, str) and (flags & re.L):
        flags = flags & ~re.L
    return original_re_compile(pattern, flags)

re.compile = patched_re_compile

# Patch fountain.main.slug regex compilation to remove re.L flag
if hasattr(fountain.main, 'slug'):
    slug = fountain.main.slug
    if isinstance(slug, re.Pattern):
        fountain.main.slug = re.compile(slug.pattern, slug.flags & ~re.L)

importlib.reload(fountain.main)

logger = logging.getLogger(__name__)

class ScriptFormat(Enum):
    FOUNTAIN = 1
    FDX = 2
    PDF = 3
    PLAINTEXT = 4

class ScriptParser:
    def __init__(self):
        self._asset_patterns = {
            'character': r'(?:close on|introducing) ([A-Z][a-z]+)',
            'prop': r'(?:using|holding) (a [a-z]+ [a-z]+)',
            'location': r'(?:establishing shot|ext\.) (.+?) (?:-|$)'
        }
    
    def parse(self, script_path: str) -> Dict:
        """Main parsing interface"""
        try:
            script_text = self._read_script(script_path)
            format_type = self._detect_format(script_path)
            
            metadata = self._extract_metadata(script_text, format_type)
            scenes = self._parse_scenes(script_text, format_type)
            assets = self._find_asset_references(script_text)
            
            return {
                'metadata': metadata,
                'scenes': scenes,
                'assets': assets
            }
        except Exception as e:
            logger.error(f"Error parsing script {script_path}: {e}")
            return {
                'metadata': {},
                'scenes': [],
                'assets': {}
            }
    
    def _read_script(self, path: str) -> str:
        """Read script based on format"""
        ext = Path(path).suffix.lower()
        try:
            if ext == '.fountain':
                return self._parse_fountain(path)
            elif ext == '.fdx':
                return self._parse_fdx(path)
            elif ext == '.pdf':
                return self._parse_pdf(path)
            else:
                with open(path, 'r') as f:
                    return f.read()
        except Exception as e:
            logger.error(f"Failed to read script file {path}: {e}")
            raise
    
    def _parse_fountain(self, path: str) -> str:
        """Parse Fountain screenplay format"""
        try:
            with open(path, 'r') as f:
                content = f.read()
            # Simple parser for Fountain format without external dependency
            lines = content.splitlines()
            elements = []
            current_element = None
            current_text = []
            for line in lines:
                line_strip = line.strip()
                if line_strip.isupper():
                    if current_element:
                        elements.append({'type': current_element, 'text': '\n'.join(current_text)})
                    current_element = line_strip
                    current_text = []
                else:
                    if current_element:
                        current_text.append(line_strip)
            if current_element:
                elements.append({'type': current_element, 'text': '\n'.join(current_text)})
            return "\n".join(
                f"{elem['type']}: {elem['text']}"
                for elem in elements
                if elem['type'] in ('SCENE HEADING', 'ACTION', 'CHARACTER')
            )
        except Exception as e:
            logger.error(f"Failed to parse Fountain script {path}: {e}")
            raise
    
    def _parse_fdx(self, path: str) -> str:
        """Parse Final Draft XML format"""
        try:
            tree = ET.parse(path)
            root = tree.getroot()
            return "\n".join(
                elem.text for elem in root.findall('.//Content')
                if elem.text and elem.text.strip()
            )
        except Exception as e:
            logger.error(f"Failed to parse FDX script {path}: {e}")
            raise
    
    def _parse_pdf(self, path: str) -> str:
        """Parse PDF scripts"""
        try:
            reader = pypdf.PdfReader(path)
            return "\n".join(page.extract_text() for page in reader.pages)
        except Exception as e:
            logger.error(f"Failed to parse PDF script {path}: {e}")
            raise
    
    def _detect_format(self, path: str) -> ScriptFormat:
        """Detect script format from extension"""
        ext = Path(path).suffix.lower()
        return {
            '.fountain': ScriptFormat.FOUNTAIN,
            '.fdx': ScriptFormat.FDX,
            '.pdf': ScriptFormat.PDF
        }.get(ext, ScriptFormat.PLAINTEXT)
    
    def _extract_metadata(self, text: str, format_type: ScriptFormat) -> Dict:
        """Extract title, author, etc."""
        if format_type == ScriptFormat.FOUNTAIN:
            title_match = re.search(r'^title: (.+)$', text, re.IGNORECASE | re.MULTILINE)
            author_match = re.search(r'^author: (.+)$', text, re.IGNORECASE | re.MULTILINE)
            return {
                'title': title_match.group(1) if title_match else '',
                'author': author_match.group(1) if author_match else ''
            }
        return {}
    
    def _parse_scenes(self, text: str, format_type: ScriptFormat) -> List[Dict]:
        """Break down script into scenes"""
        if format_type == ScriptFormat.FOUNTAIN:
            scenes = re.split(r'\n{2,}', text)
            return [{
                'heading': scene.split('\n')[0],
                'content': '\n'.join(scene.split('\n')[1:])
            } for scene in scenes if scene.strip()]
        return []
    
    def _find_asset_references(self, text: str) -> Dict[str, List[str]]:
        """Find potential asset references in script"""
        assets = {}
        for asset_type, pattern in self._asset_patterns.items():
            matches = re.findall(pattern, text, re.IGNORECASE)
            assets[asset_type] = list(set(matches))
        return assets
