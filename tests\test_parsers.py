import pytest
from pathlib import Path
from src.core.script_parser import <PERSON>riptParser

@pytest.fixture
def parser():
    return ScriptParser()

@pytest.fixture
def pdf_sample(tmp_path):
    # Create a minimal PDF file for testing
    from fpdf import FPDF
    pdf_path = tmp_path / "sample.pdf"
    pdf = FPDF()
    pdf.add_page()
    pdf.set_font("Arial", size=12)
    pdf.cell(200, 10, "Scene 1: A room", ln=True)
    pdf.output(str(pdf_path))
    return str(pdf_path)

def test_fountain_parsing(parser, tmp_path):
    fountain_script = """
    Title: Test Script
    Author: ProShooto
    
    INT. HOUSE - DAY
    
    A wooden table sits in the center.
    """
    
    test_file = tmp_path / "test.fountain"
    test_file.write_text(fountain_script)
    
    result = parser.parse(str(test_file))
    assert result['metadata']['title'] == "Test Script"
    assert "wooden table" in str(result['assets'])

def test_pdf_parsing(parser, pdf_sample):
    result = parser.parse(pdf_sample)
    assert len(result['scenes']) > 0

def test_parse_nonexistent_file(parser):
    # Test parsing a file that does not exist to check error handling
    result = parser.parse("nonexistent_file.fountain")
    assert result['metadata'] == {}
    assert result['scenes'] == []
    assert result['assets'] == {}

def test_parse_invalid_format(parser, tmp_path):
    # Test parsing a file with invalid content to check error handling
    invalid_file = tmp_path / "invalid.fountain"
    invalid_file.write_text("\x00\x01\x02")
    result = parser.parse(str(invalid_file))
    assert result['metadata'] == {}
    assert result['scenes'] == []
    assert result['assets'] == {}
